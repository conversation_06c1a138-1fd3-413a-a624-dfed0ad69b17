import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable standalone output for Docker deployment in WSO2 Choreo
  output: 'standalone',

  // Optimize for production deployment
  poweredByHeader: false,

  // Configure for potential CDN usage in Choreo
  assetPrefix: process.env.NEXT_PUBLIC_ASSET_PREFIX || '',

  // Environment variables validation
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
    NEXT_PUBLIC_CHOREO_API_URL: process.env.NEXT_PUBLIC_CHOREO_API_URL,
  },

  // Experimental features that might be useful for Choreo
  experimental: {
    // Enable if you need server actions later
    // serverActions: true,
  },
};

export default nextConfig;
